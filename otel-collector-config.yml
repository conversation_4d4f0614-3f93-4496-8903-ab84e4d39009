receivers:
  otlp:
    protocols:
      grpc:
      http:

exporters:
  otlp/jaeger:
    endpoint: "jaeger:4317"
    tls:
      insecure: true
  otlp/prometheus:
    endpoint: "prometheus:9090"
    tls:
      insecure: true
  otlp/elastic:
    endpoint: "http://elasticsearch:9200"
    headers:
      Authorization: "Basic ZWxhc3RpYzpyb290" # elastic:changeme base64 encoded
    tls:
      insecure: true
    sending_queue:
      queue_size: 500
    retry_on_failure:
      enabled: true

service:
  pipelines:
    traces:
      receivers: [otlp]
      exporters: [otlp/jaeger, otlp/elastic]
    metrics:
      receivers: [otlp]
      exporters: [otlp/prometheus]
    logs:
      receivers: [otlp]
      exporters: [otlp/elastic]